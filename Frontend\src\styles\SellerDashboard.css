.dashboard-container {
  padding: 0;
  background-color: transparent;
}

.dashboard-container .stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--heading5);
  margin-bottom: var(--heading4);
}

.dashboard-container .stats-card {
  border-radius: var(--border-radius);
  padding: var(--heading5);
  background-color: var(--white);
  box-shadow: var(--box-shadow-light);
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-container .detailmain {
  display: flex;
  flex-direction: column;
  align-content: center;
  justify-content: center;
  align-items: flex-start;
  gap: 5px;
}

.dashboard-container .icon-round {
  border-radius: 50%;
  background-color: #bf83ff;
  padding: 10px;
  font-size: var(--heading5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
}

.dashboard-container .stats-card:hover {
  transform: scale(1.02);
  box-shadow: var(--box-shadow);
}

.dashboard-container .stats-card h2 {
  font-size: var(--heading5);
  color: var(--text-color);
  font-weight: 700;
}

.dashboard-container .stats-card p {
  margin: 0;
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

.dashboard-container .stats-card.purple {
  background-image: url("../assets/images/buyerdashboardone.svg");
  object-fit: cover;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-color: #f3e8ff;
  border-radius: var(--border-radius-large);
}

.dashboard-container .icon-round.purple {
  background-color: #bf83ff;
}

.dashboard-container .stats-card.orange {
  background-image: url("../assets/images/buyerdashboardtwo.svg");
  object-fit: cover;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-color: #fff2da;
  border-radius: var(--border-radius-large);
}

.dashboard-container .icon-round.orange {
  background-color: #ff947a;
}

.dashboard-container .stats-card.green {
  background-image: url("../assets/images/buyerdashboardthree.svg");
  object-fit: cover;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-color: #dcfce7;
  border-radius: var(--border-radius-large);
}

.dashboard-container .icon-round.green {
  background-color: #3cd856;
}

.dashboard-container .stats-card.blue {
  background-image: url("../assets/images/buyerdashboardfour.svg");
  object-fit: cover;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  background-color: #e0f2fe;
  border-radius: var(--border-radius-large);
}

.dashboard-container .icon-round.blue {
  background-color: #73a0b1;
}

.dashboard-container .section {
  margin-bottom: var(--heading4);
  padding: var(--heading5);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  box-shadow: var(--box-shadow-light);
}

.dashboard-container .section:last-child {
  margin-bottom: 0;
}

.dashboard-container .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--basefont);
  padding-bottom: var(--smallfont);
  border-bottom: 1px solid var(--light-gray);
}

.dashboard-container .section-header h3 {
  font-size: var(--heading6);
  color: var(--secondary-color);
  font-weight: 600;
  margin: 0;
}

.dashboard-container .section-header a {
  font-size: var(--smallfont);
  color: var(--btn-color);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
  text-align: end;
}

.dashboard-container .section-header a:hover {
  color: var(--primary-color);
}

.dashboard-container .table-container {
  overflow-x: auto;
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--light-gray);
}

.dashboard-container table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--smallfont);
  background-color: var(--white);
  margin: 0;
}

.dashboard-container th,
.dashboard-container td {
  padding: var(--smallfont) var(--extrasmallfont);
  text-align: left;
  border-bottom: 1px solid var(--light-gray);
  vertical-align: middle;
  white-space: nowrap;
}

.dashboard-container th {
  background-color: var(--bg-gray);
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--smallfont);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: "Poppins", sans-serif;
}

.dashboard-container .dashboard-container tbody tr:last-child td {
  border-bottom: none;
}

.dashboard-container .video-title {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  font-weight: 500;
  max-width: 200px;
}
.dashboard-container .video-title span {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.dashboard-container .video-icon {
  color: var(--btn-color);
  font-size: var(--basefont);
  flex-shrink: 0;
}

/* Action Icons */
.dashboard-container .action-icons {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
  justify-content: center;
}

.dashboard-container .action-icon {
  font-size: var(--heading6);
  color: black;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

.dashboard-container .action-icon:hover {
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: scale(1.02);
}

/* Toggle Switch */
.dashboard-container .switch {
  position: relative;
  display: inline-block;
  width: 35px;
  height: 18px;
}

.dashboard-container .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.dashboard-container .slider {
  position: absolute;
  cursor: pointer;
  background-color: var(--light-gray);
  border-radius: 24px;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: all 0.3s ease;
  border: 2px solid var(--light-gray);
}

.dashboard-container .slider::before {
  position: absolute;
  content: "";
  height: 12px;
  width: 12px;
  left: 2px;
  bottom: 1.8px;
  background-color: var(--white);
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dashboard-container input:checked + .slider {
  background-color: var(--btn-color);
  border-color: var(--btn-color);
}

.dashboard-container input:checked + .slider::before {
  transform: translateX(18px);
}

.dashboard-container .slider.round {
  border-radius: 34px;
}
.dashboard-container .status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--smallfont);
  font-weight: 500;
  text-align: center;
  min-width: 80px;
  display: inline-block;
}
.dashboard-container .status-badge.status-pending {
  background-color: #fff3cd;
  color: #856404;
}
.dashboard-container .status-badge.status-accepted {
  background-color: #d4edda;
  color: #155724;
}
.dashboard-container .status-badge.status-expired {
  background-color: #f8d7da;
  color: #721c24;
}
.dashboard-container .status-badge.status-cancelled {
background-color: #f3f4f6;
    color: #6b7280;
}
.dashboard-container .seller-dashboardstrategy-thumbnail {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 8px;
  margin-right: 12px;
}
/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-container .stats-container {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--basefont);
  }

  .dashboard-container .section {
    padding: var(--basefont);
  }

  .dashboard-container th,
  .dashboard-container td {
    padding: var(--extrasmallfont) var(--extrasmallfont);
    font-size: var(--extrasmallfont);
  }
}

@media (max-width: 768px) {
  .dashboard-container .stats-container {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--smallfont);
  }

  .dashboard-container .section {
    padding: var(--smallfont);
    margin-bottom: var(--basefont);
  }

  .dashboard-container .section-header {
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }

  .dashboard-container .table-container {
    font-size: var(--extrasmallfont);
  }

  .dashboard-container .video-title {

    gap: var(--extrasmallfont);
  }

  .dashboard-container .action-icons {
    gap: var(--extrasmallfont);
  }
}

@media (max-width: 480px) {
  .dashboard-container .stats-container {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
  }

  .dashboard-container .stats-card {
    padding: var(--basefont);
  }

  .dashboard-container .stats-card h2 {
    font-size: var(--heading5);
  }

  /* Stack table content for mobile */
  .dashboard-container .table-container {
    overflow-x: scroll;
  }

  .dashboard-container table {
    min-width: 600px;
  }
}

const ErrorResponse = require("../utils/errorResponse");
const Content = require("../models/Content");
const User = require("../models/User");
const { validationResult } = require("express-validator");
const { generatePreview, canGeneratePreview, cleanupPreviewFile, ensurePreviewDirectories } = require("../utils/previewGenerator");
const { getContentFileAccess } = require("../utils/accessControl");
const sendAdminNotification = require('../utils/sendAdminNotification');
const { isS3Url } = require("../utils/storageHelper");
const { cleanupContentFiles } = require("../utils/fileCleanup");
const path = require("path");
const { isDateInFutureUTC, compareUTCDates } = require("../utils/dateUtils");

// Ensure preview directories exist on module load
ensurePreviewDirectories();

// @desc    Get all content
// @route   GET /api/content
// @access  Public
exports.getAllContent = async (req, res, next) => {
  try {
    let query;

    // Copy req.query
    const reqQuery = { ...req.query };

    // Fields to exclude
    const removeFields = [
      "page",
      "limit",
      "sport",
      "priceMin",
      "priceMax",
      "sortBy",
      "search",
      "contentType",
      "difficulty",
      "saleType",
    ];

    // Loop over removeFields and delete them from reqQuery
    removeFields.forEach((param) => delete reqQuery[param]);

    // Create query string
    let queryStr = JSON.stringify(reqQuery);

    // Create operators ($gt, $gte, etc)
    queryStr = queryStr.replace(
      /\b(gt|gte|lt|lte|in)\b/g,
      (match) => `$${match}`
    );

    // Parse the query string
    let queryObj = JSON.parse(queryStr);

    // Base query - show both Published and Draft content
    const baseQuery = {
      status: "Published",
      visibility: "Public",
      isActive: { $in: [1, true] },
      // Exclude custom content from public listings
      isCustomContent: { $ne: true },
      // Exclude sold content and ended auctions from public listing
      $and: [
        {
          $or: [
            { isSold: { $ne: true } },
            { isSold: { $exists: false } }
          ]
        },
        {
          $or: [
            // Content is not auction type
            { saleType: "Fixed" },
            // Auction content that hasn't ended yet
            {
              $and: [
                { saleType: { $in: ["Auction", "Both"] } },
                {
                  $or: [
                    { auctionStatus: { $ne: "Ended" } },
                    { auctionStatus: { $exists: false } }
                  ]
                }
              ]
            }
          ]
        }
      ],
      ...queryObj,
    };

    // Handle sport filter
    if (req.query.sport) {
      baseQuery.sport = req.query.sport;
    }

    // Handle content type filter
    if (req.query.contentType) {
      const contentTypes = Array.isArray(req.query.contentType)
        ? req.query.contentType
        : [req.query.contentType];
      if (contentTypes.length > 0) {
        baseQuery.contentType = { $in: contentTypes };
      }
    }
    // Handle difficulty filter
    if (req.query.difficulty) {
      const difficulties = Array.isArray(req.query.difficulty)
        ? req.query.difficulty
        : [req.query.difficulty];
      if (difficulties.length > 0) {
        baseQuery.difficulty = { $in: difficulties };
      }
    }
    // Handle sale type filter
    if (req.query.saleType) {
      const saleTypes = Array.isArray(req.query.saleType)
        ? req.query.saleType
        : [req.query.saleType];
      if (saleTypes.length > 0) {
        baseQuery.saleType = { $in: saleTypes };
      }
    }

    // Handle price range - considering both price and auction basePrice
    if (req.query.priceMin || req.query.priceMax) {
      // Create aggregation pipeline stage to calculate effective price
      const priceMin = req.query.priceMin ? parseFloat(req.query.priceMin) : 0;
      const priceMax = req.query.priceMax ? parseFloat(req.query.priceMax) : Number.MAX_SAFE_INTEGER;

      // Add a condition that uses the same logic as the categories endpoint
      baseQuery.$expr = {
        $and: [
          {
            $gte: [
              {
                $cond: {
                  if: { $and: [{ $ne: ["$saleType", "Fixed"] }, { $gt: ["$auctionDetails.basePrice", 0] }] },
                  then: "$auctionDetails.basePrice",
                  else: { $ifNull: ["$price", 0] }
                }
              },
              priceMin
            ]
          },
          {
            $lte: [
              {
                $cond: {
                  if: { $and: [{ $ne: ["$saleType", "Fixed"] }, { $gt: ["$auctionDetails.basePrice", 0] }] },
                  then: "$auctionDetails.basePrice",
                  else: { $ifNull: ["$price", 0] }
                }
              },
              priceMax
            ]
          }
        ]
      };
    }

    // Handle search
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, "i");
      baseQuery.$or = [
        { title: searchRegex },
        { description: searchRegex },
        { tags: searchRegex },
      ];
    }

    // Count total before pagination
    const total = await Content.countDocuments(baseQuery);

    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = 9; // Fixed limit of 9 items per page
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;

    // Handle sorting - use aggregation pipeline for price sorting to support both fixed and auction content
    let content;
    if (req.query.sortBy && (req.query.sortBy === "price_asc" || req.query.sortBy === "price_desc")) {
      // Use aggregation pipeline for price sorting to handle both price and basePrice
      const sortDirection = req.query.sortBy === "price_asc" ? 1 : -1;

      const aggregationPipeline = [
        { $match: baseQuery },
        {
          $addFields: {
            effectivePrice: {
              $cond: {
                if: {
                  $and: [
                    { $ne: ["$saleType", "Fixed"] },
                    { $gt: ["$auctionDetails.basePrice", 0] }
                  ]
                },
                then: "$auctionDetails.basePrice",
                else: { $ifNull: ["$price", 0] }
              }
            }
          }
        },
        { $sort: { effectivePrice: sortDirection } },
        { $skip: startIndex },
        { $limit: limit },
        {
          $lookup: {
            from: "users",
            let: { sellerId: "$seller" },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ["$_id", "$$sellerId"] }
                }
              },
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  profileImage: 1,
                  isVerified: 1
                }
              }
            ],
            as: "seller"
          }
        },
        {
          $unwind: {
            path: "$seller",
            preserveNullAndEmptyArrays: true
          }
        }
      ];

      content = await Content.aggregate(aggregationPipeline);
    } else {
      // Use regular query for non-price sorting
      const sortMap = {
        newest: "-createdAt",
        oldest: "createdAt",
        rating: "-averageRating",
      };
      const sortField = sortMap[req.query.sortBy] || "-createdAt";

      const query = Content.find(baseQuery)
        .sort(sortField)
        .skip(startIndex)
        .limit(limit)
        .populate({
          path: "seller",
          select: "firstName lastName profileImage isVerified",
        });

      content = await query;
    }

    // Pagination result
    const pagination = {};

    if (endIndex < total) {
      pagination.next = {
        page: page + 1,
        limit,
      };
    }

    if (startIndex > 0) {
      pagination.prev = {
        page: page - 1,
        limit,
      };
    }

    res.status(200).json({
      success: true,
      count: content.length,
      total,
      pagination,
      data: content,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get single content
// @route   GET /api/content/:id
// @access  Public
exports.getContent = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id).populate({
      path: "seller",
      select: "firstName lastName profileImage isVerified",
    });

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Check if content is published or user is the seller or admin
    if (
      content.status !== "Published" &&
      (!req.user ||
        (req.user.id !== content.seller._id.toString() &&
          req.user.role !== "admin" &&
          req.user.role !== "buyer"))
    ) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Get appropriate file access based on user's purchase status
    const userId = req.user ? req.user.id : null;
    const userRole = req.user ? req.user.role : null;

    const fileAccess = await getContentFileAccess(content, userId, userRole);

    // Create response data with access-controlled file information
    const responseData = {
      ...content.toObject(),
      fileAccess: {
        accessType: fileAccess.accessType,
        canAccessFull: fileAccess.canAccessFull,
        hasPreview: fileAccess.hasPreview,
        reason: fileAccess.reason
      }
    };

    // Control access to file URLs based on user's purchase status
    if (fileAccess.accessType === 'full') {
      // User has full access - show both original and preview URLs
      responseData.accessibleFileUrl = content.fileUrl;
      responseData.fileUrl = content.fileUrl;
      responseData.previewUrl = content.previewUrl;
    } else if (fileAccess.accessType === 'preview' && content.previewUrl) {
      // User has preview access only - hide original file URL
      responseData.accessibleFileUrl = content.previewUrl;
      responseData.previewUrl = content.previewUrl;
      delete responseData.fileUrl; // Remove access to original file URL
    } else {
      // No access - hide all file URLs
      responseData.accessibleFileUrl = null;
      delete responseData.fileUrl;
      delete responseData.previewUrl;
    }

    res.status(200).json({
      success: true,
      data: responseData,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create content
// @route   POST /api/content
// @access  Private/Seller
exports.createContent = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    // Add user to req.body
    req.body.seller = req.user.id;

    // Validate auction dates if auction type is selected
    if (req.body.saleType === "Auction" && req.body.auctionDetails) {
      // Validate auction start date
      if (req.body.auctionDetails.auctionStartDate) {
        if (!isDateInFutureUTC(req.body.auctionDetails.auctionStartDate)) {
          return next(
            new ErrorResponse(
              "Auction start date must be in the future",
              400
            )
          );
        }
      }

      // Validate auction end date
      if (req.body.auctionDetails.auctionEndDate && req.body.auctionDetails.auctionStartDate) {
        if (compareUTCDates(req.body.auctionDetails.auctionEndDate, req.body.auctionDetails.auctionStartDate) <= 0) {
          return next(
            new ErrorResponse(
              "Auction end date must be after start date",
              400
            )
          );
        }
      }
    }

    // Check if user has seller access (using effective role like the auth middleware)
    const user = await User.findById(req.user.id);
    const effectiveRole = user.role === 'admin' ? user.role : user.activeRole;

    if (effectiveRole !== "seller" && effectiveRole !== "admin") {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to create content`,
          403
        )
      );
    }

    // Check if seller has completed Stripe Connect onboarding (for non-admin users)
    if (effectiveRole === "seller") {
      if (!user.paymentInfo?.stripeConnectId) {
        return next(
          new ErrorResponse(
            `Payment setup required. Please complete your Stripe Connect onboarding before creating content.`,
            400
          )
        );
      }

      // Verify Stripe Connect account exists and is properly set up
      try {
        const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
        const connectAccount = await stripe.accounts.retrieve(user.paymentInfo.stripeConnectId);

        if (!connectAccount.details_submitted || !connectAccount.charges_enabled) {
          return next(
            new ErrorResponse(
              `Payment setup incomplete. Please complete your Stripe Connect onboarding to start creating content.`,
              400
            )
          );
        }
      } catch (stripeError) {
        console.error('Stripe Connect verification error:', stripeError);
        return next(
          new ErrorResponse(
            `Invalid payment setup. Please complete your Stripe Connect onboarding again.`,
            400
          )
        );
      }
    }

    // Set initial preview status
    req.body.previewStatus = 'pending';

    // Set URL generation timestamps for S3 files
    if (req.body.fileUrl && isS3Url(req.body.fileUrl)) {
      req.body.fileUrlGeneratedAt = new Date();
      console.log(`[Content] Setting fileUrlGeneratedAt for S3 file: ${req.body.fileUrl}`);
    }

    if (req.body.previewUrl && isS3Url(req.body.previewUrl)) {
      req.body.previewUrlGeneratedAt = new Date();
      console.log(`[Content] Setting previewUrlGeneratedAt for S3 file: ${req.body.previewUrl}`);
    }

    // Create content first without waiting for preview generation
    const content = await Content.create(req.body);

    // Generate preview asynchronously if supported content type
    if (req.body.fileUrl && req.body.contentType) {
      // Don't await this - let it run in background
      generatePreviewAsync(content._id, req.body.fileUrl, req.body.contentType)
        .catch(error => {
          console.error(`[Content] Background preview generation failed for content ${content._id}:`, error);
        });
    }

    // Send email notification to all admin users (don't await - run in background)
    const seller = await User.findById(req.user.id).select('firstName lastName email');
    sendAdminNotification({
      subject: 'New Content Submitted for Review - XO Sports Hub',
      message: `A new content has been submitted by ${seller.firstName} ${seller.lastName} and is pending review.\n\nContent Title: ${content.title}\nSport: ${content.sport}\nContent Type: ${content.contentType}\nSeller: ${seller.firstName} ${seller.lastName} (${seller.email})\n\nPlease log in to the admin panel to review and approve this content.`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">New Content Submitted for Review</h2>

          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #007bff; margin-top: 0;">Content Details</h3>
            <p><strong>Title:</strong> ${content.title}</p>
            <p><strong>Sport:</strong> ${content.sport}</p>
            <p><strong>Content Type:</strong> ${content.contentType}</p>
            <p><strong>Price:</strong> $${content.price}</p>
            <p><strong>Status:</strong> ${content.status}</p>
          </div>

          <div style="background-color: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #28a745; margin-top: 0;">Seller Information</h3>
            <p><strong>Name:</strong> ${seller.firstName} ${seller.lastName}</p>
            <p><strong>Email:</strong> ${seller.email}</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <p style="color: #666;">Please log in to the admin panel to review and approve this content.</p>
            <a href="${process.env.FRONTEND_URL}/admin/content"
               style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Review Content
            </a>
          </div>

          <hr style="border: none; border-top: 1px solid #dee2e6; margin: 30px 0;">
          <p style="color: #6c757d; font-size: 12px; text-align: center;">
            This is an automated notification from XO Sports Hub Admin System.
          </p>
        </div>
      `
    }).catch(error => {
      console.error(`[Content] Failed to send admin notification for content ${content._id}:`, error);
    });

    // Return response immediately
    res.status(201).json({
      success: true,
      data: content,
      message: 'Content created successfully. Preview will be generated in the background.'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get preview generation status
// @route   GET /api/content/:id/preview-status
// @access  Public
exports.getPreviewStatus = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id).select('previewStatus previewError previewUrl');

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    res.status(200).json({
      success: true,
      data: {
        previewStatus: content.previewStatus,
        previewError: content.previewError,
        hasPreview: !!content.previewUrl
      }
    });
  } catch (err) {
    next(err);
  }
};

// Helper function for asynchronous preview generation
const generatePreviewAsync = async (contentId, fileUrl, contentType) => {
  try {
    console.log(`[Content] Starting background preview generation for content: ${contentId}`);

    // Extract filename from fileUrl
    const fileName = fileUrl.split('/').pop();

    // Check if preview can be generated
    if (!canGeneratePreview(contentType, fileName)) {
      console.log(`[Content] Preview generation not supported for ${contentType} file: ${fileName}`);
      await Content.findByIdAndUpdate(contentId, {
        previewStatus: 'not_supported'
      });
      return;
    }

    // Update status to processing
    await Content.findByIdAndUpdate(contentId, {
      previewStatus: 'processing'
    });

    // Determine if file is on S3 or local
    const isS3Upload = isS3Url(fileUrl);
    console.log(`[Content] Is S3 upload: ${isS3Upload}`);

    // Generate preview with timeout
    const previewPromise = generatePreview(
      contentType,
      isS3Upload ? fileUrl : path.join('./uploads/', fileName),
      fileName,
      isS3Upload
    );

    // Set timeout for preview generation based on content type and file size limits
    const getTimeoutForContentType = (contentType) => {
      switch (contentType.toLowerCase()) {
        case 'video':
          return 900000; // 15 minutes for videos (1GB files need more time for download + processing)
        case 'pdf':
        case 'document':
          return 600000; // 10 minutes for PDFs (50MB files with S3 download + processing)
        default:
          return 300000; // 5 minutes default
      }
    };

    const timeoutMs = getTimeoutForContentType(contentType);
    console.log(`[Content] Setting ${timeoutMs / 1000}s timeout for ${contentType} preview generation`);

    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Preview generation timeout')), timeoutMs);
    });

    const previewUrl = await Promise.race([previewPromise, timeoutPromise]);

    if (previewUrl) {
      console.log(`[Content] Background preview generated successfully: ${previewUrl}`);

      // Prepare update data
      const updateData = {
        previewUrl: previewUrl,
        previewStatus: 'completed'
      };

      // Set preview URL generation timestamp for S3 files
      if (isS3Url(previewUrl)) {
        updateData.previewUrlGeneratedAt = new Date();
        console.log(`[Content] Setting previewUrlGeneratedAt for background generated S3 preview: ${previewUrl}`);
      }

      await Content.findByIdAndUpdate(contentId, updateData);
    } else {
      console.log(`[Content] Background preview generation returned null for ${fileName}`);
      await Content.findByIdAndUpdate(contentId, {
        previewStatus: 'failed'
      });
    }
  } catch (error) {
    console.error(`[Content] Background preview generation failed:`, error);
    await Content.findByIdAndUpdate(contentId, {
      previewStatus: 'failed',
      previewError: error.message
    });
  }
};

// @desc    Update content
// @route   PUT /api/content/:id
// @access  Private/Seller
exports.updateContent = async (req, res, next) => {
  try {
    let content = await Content.findById(req.params.id);

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is content seller
    if (
      content.seller.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to update this content`,
          403
        )
      );
    }

    // Validate auction dates if auction type is selected
    if (req.body.saleType === "Auction" && req.body.auctionDetails) {
      // Validate auction start date
      if (req.body.auctionDetails.auctionStartDate) {
        if (!isDateInFutureUTC(req.body.auctionDetails.auctionStartDate)) {
          return next(
            new ErrorResponse(
              "Auction start date must be in the future",
              400
            )
          );
        }
      }

      // Validate auction end date
      if (req.body.auctionDetails.auctionEndDate && req.body.auctionDetails.auctionStartDate) {
        if (compareUTCDates(req.body.auctionDetails.auctionEndDate, req.body.auctionDetails.auctionStartDate) <= 0) {
          return next(
            new ErrorResponse(
              "Auction end date must be after start date",
              400
            )
          );
        }
      }
    }

    // Check if file URL is being updated and regenerate preview if needed
    let previewUrl = content.previewUrl; // Keep existing preview by default

    // Set URL generation timestamps for updated S3 files
    if (req.body.fileUrl && req.body.fileUrl !== content.fileUrl && isS3Url(req.body.fileUrl)) {
      req.body.fileUrlGeneratedAt = new Date();
      console.log(`[Update] Setting fileUrlGeneratedAt for updated S3 file: ${req.body.fileUrl}`);
    }

    if (req.body.fileUrl && req.body.fileUrl !== content.fileUrl) {
      console.log(`[Update] File URL changed, regenerating preview for content: ${content.title}`);

      try {
        // Extract filename from new fileUrl
        const fileName = req.body.fileUrl.split('/').pop();
        const contentType = req.body.contentType || content.contentType;

        // Check if preview can be generated for the new file
        if (canGeneratePreview(contentType, fileName)) {
          console.log(`[Update] Generating new preview for ${contentType} file: ${fileName}`);

          // Determine if file is on S3 or local
          const isS3Upload = isS3Url(req.body.fileUrl);

          // Generate new preview
          previewUrl = await generatePreview(
            contentType,
            isS3Upload ? req.body.fileUrl : path.join('./uploads/', fileName),
            fileName,
            isS3Upload
          );

          if (previewUrl) {
            console.log(`[Update] New preview generated successfully: ${previewUrl}`);
            req.body.previewUrl = previewUrl;

            // Set preview URL generation timestamp for S3 files
            if (isS3Url(previewUrl)) {
              req.body.previewUrlGeneratedAt = new Date();
              console.log(`[Update] Setting previewUrlGeneratedAt for new S3 preview: ${previewUrl}`);
            }
          } else {
            console.log('[Update] Preview generation returned null - keeping existing preview');
            req.body.previewUrl = content.previewUrl;
          }
        } else {
          console.log(`[Update] Preview generation not supported for ${contentType} file: ${fileName}`);
          req.body.previewUrl = null; // Clear preview if new file doesn't support it
        }
      } catch (previewError) {
        console.error('[Update] Error regenerating preview:', previewError);
        // Don't fail update if preview generation fails, just keep existing preview
        req.body.previewUrl = content.previewUrl;
      }
    }

    content = await Content.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });

    res.status(200).json({
      success: true,
      data: content,
      message: req.body.previewUrl && req.body.previewUrl !== previewUrl ?
        'Content updated with new preview' : 'Content updated successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete content
// @route   DELETE /api/content/:id
// @access  Private/Seller
exports.deleteContent = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id);

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Authorization check
    if (
      content.seller.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to delete this content`,
          403
        )
      );
    }

    // Clean up all associated files (thumbnails, previews, main content)
    try {
      console.log(`[Delete] Starting comprehensive file cleanup for content: ${content.title}`);

      // Use comprehensive file cleanup with skipMainContent option for soft delete
      // This preserves the main content file for audit purposes while cleaning up thumbnails and previews
      const cleanupResults = await cleanupContentFiles(content, {
        skipMainContent: true // Keep main content file for audit purposes in soft delete
      });

      console.log(`[Delete] File cleanup completed. Files processed: ${cleanupResults.totalFiles}, Successful: ${cleanupResults.successfulDeletions}, Failed: ${cleanupResults.failedDeletions}`);

      if (cleanupResults.failedDeletions > 0) {
        console.warn(`[Delete] Some files failed to delete:`, cleanupResults.errors);
      }

    } catch (cleanupError) {
      console.error('[Delete] Error during comprehensive file cleanup:', cleanupError);
      // Don't fail deletion if cleanup fails - log error and continue
    }

    // Soft delete by marking isActive = -1
    content.isActive = -1;
    await content.save();

    res.status(200).json({
      success: true,
      data: {},
      message: "Content soft-deleted successfully",
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get seller content
// @route   GET /api/content/seller/me
// @access  Private/Seller
exports.getSellerContent = async (req, res, next) => {
  try {
    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 9;
    const startIndex = (page - 1) * limit;

    // Get total count first
    const total = await Content.countDocuments({
      seller: req.user.id,
      isActive: 1
    });

    // Get paginated content
    const content = await Content.find({
      seller: req.user.id,
      isActive: 1
    })
      .sort("-createdAt") // Sort by creation date in descending order (newest first)
      .skip(startIndex)
      .limit(limit)
      .lean(); // Use lean() to get plain objects instead of Mongoose documents

    // Convert ObjectIds and Dates to strings for proper JSON serialization
    const serializedContent = content.map(item => ({
      ...item,
      _id: item._id.toString(),
      seller: item.seller.toString(),
      createdAt: item.createdAt ? item.createdAt.toISOString() : null,
      lastUpdated: item.lastUpdated ? item.lastUpdated.toISOString() : null,
      publishedDate: item.publishedDate ? item.publishedDate.toISOString() : null,
      // Add id field for consistency with frontend expectations
      id: item._id.toString()
    }));

    // Calculate total pages
    const totalPages = Math.ceil(total / limit);

    res.status(200).json({
      success: true,
      count: total,
      totalPages,
      currentPage: page,
      data: serializedContent,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get single seller content (seller can view their own content regardless of status)
// @route   GET /api/content/seller/:id
// @access  Private/Seller
exports.getSellerContentById = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id)
      .populate({
        path: "seller",
        select: "firstName lastName profileImage isVerified",
      })
      .lean(); // Use lean() to get plain objects instead of Mongoose documents

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is content seller or admin
    if (
      content.seller._id.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to view this content`,
          403
        )
      );
    }

    // Convert ObjectIds and Dates to strings for proper JSON serialization
    const serializedContent = {
      ...content,
      _id: content._id.toString(),
      seller: {
        ...content.seller,
        _id: content.seller._id.toString()
      },
      createdAt: content.createdAt ? content.createdAt.toISOString() : null,
      lastUpdated: content.lastUpdated ? content.lastUpdated.toISOString() : null,
      publishedDate: content.publishedDate ? content.publishedDate.toISOString() : null,
      // Add id field for consistency with frontend expectations
      id: content._id.toString()
    };

    res.status(200).json({
      success: true,
      data: serializedContent,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get content categories
// @route   GET /api/content/categories
// @access  Public
exports.getContentCategories = async (req, res, next) => {
  try {
    // Get unique sport types
    const sports = await Content.distinct("sport", {
      status: "Published",
      visibility: "Public",
    });

    // Get unique content types
    const contentTypes = await Content.distinct("contentType", {
      status: "Published",
      visibility: "Public",
    });

    // Get unique difficulty levels
    const difficultyLevels = await Content.distinct("difficulty", {
      status: "Published",
      visibility: "Public",
    });

    // Get price ranges including both fixed prices and auction base prices
    const priceStats = await Content.aggregate([
      {
        $match: {
          status: "Published",
          visibility: "Public",
          $or: [
            { price: { $exists: true, $ne: null, $gt: 0 } },
            { "auctionDetails.basePrice": { $exists: true, $ne: null, $gt: 0 } }
          ]
        },
      },
      {
        $addFields: {
          effectivePrice: {
            $cond: {
              if: { $and: [{ $ne: ["$saleType", "Fixed"] }, { $gt: ["$auctionDetails.basePrice", 0] }] },
              then: "$auctionDetails.basePrice",
              else: { $ifNull: ["$price", 0] }
            }
          }
        }
      },
      {
        $match: {
          effectivePrice: { $gt: 0 }
        }
      },
      {
        $group: {
          _id: null,
          minPrice: { $min: "$effectivePrice" },
          maxPrice: { $max: "$effectivePrice" },
          avgPrice: { $avg: "$effectivePrice" },
        },
      },
    ]);

    // Get popular tags
    const tagCounts = await Content.aggregate([
      {
        $match: {
          status: "Published",
          visibility: "Public",
          tags: { $exists: true, $ne: [] },
        },
      },
      { $unwind: "$tags" },
      {
        $group: {
          _id: "$tags",
          count: { $sum: 1 },
        },
      },
      { $sort: { count: -1 } },
      { $limit: 20 },
    ]);

    const popularTags = tagCounts.map((tag) => tag._id);

    res.status(200).json({
      success: true,
      data: {
        sports,
        contentTypes,
        difficultyLevels,
        priceRange: priceStats[0] || { minPrice: 0, maxPrice: 0, avgPrice: 0 },
        popularTags,
      },
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Toggle content active status
// @route   PUT /api/content/:id/toggle-status
// @access  Private/Seller
exports.toggleContentStatus = async (req, res, next) => {
  try {
    let content = await Content.findById(req.params.id);

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is content seller
    if (
      content.seller.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to update this content`,
          403
        )
      );
    }

    // Toggle the isActive status
    const newStatus = content.isActive === 1 ? 0 : 1;

    content = await Content.findByIdAndUpdate(
      req.params.id,
      { isActive: newStatus },
      {
        new: true,
        runValidators: true,
      }
    );

    res.status(200).json({
      success: true,
      data: content,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get trending content
// @route   GET /api/content/trending
// @access  Public
exports.getTrendingContent = async (req, res, next) => {
  try {
    // Base query for available content (not sold or ended auctions)
    const availableContentQuery = {
      status: "Published",
      visibility: "Public",
      isActive: 1,
      $and: [
        {
          $or: [
            { isSold: { $ne: true } },
            { isSold: { $exists: false } }
          ]
        },
        {
          $or: [
            // Content is not auction type
            { saleType: "Fixed" },
            // Auction content that hasn't ended yet
            {
              $and: [
                { saleType: { $in: ["Auction", "Both"] } },
                {
                  $or: [
                    { auctionStatus: { $ne: "Ended" } },
                    { auctionStatus: { $exists: false } }
                  ]
                }
              ]
            }
          ]
        }
      ]
    };

    // Get content with highest ratings
    const topRated = await Content.find({
      ...availableContentQuery,
      averageRating: { $exists: true, $gte: 4 },
    })
      .sort("-averageRating")
      .limit(5)
      .populate({
        path: "seller",
        select: "firstName lastName profileImage isVerified",
      });

    // Get most recently published content
    const newest = await Content.find(availableContentQuery)
      .sort("-createdAt")
      .limit(5)
      .populate({
        path: "seller",
        select: "firstName lastName profileImage isVerified",
      });

    // Get most purchased content (would require aggregation with orders)
    // This is a placeholder - you would need to implement the actual query
    const popular = await Content.find(availableContentQuery)
      .sort("-createdAt")
      .limit(5)
      .populate({
        path: "seller",
        select: "firstName lastName profileImage isVerified",
      });

    res.status(200).json({
      success: true,
      data: {
        topRated,
        newest,
        popular,
      },
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get secure content file access
// @route   GET /api/content/:id/access
// @access  Private
exports.getContentAccess = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id);

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Check if content is published
    if (content.status !== "Published") {
      return next(
        new ErrorResponse(`Content is not available`, 404)
      );
    }

    // Get user's access level
    const userId = req.user ? req.user.id : null;
    const userRole = req.user ? req.user.role : null;

    const fileAccess = await getContentFileAccess(content, userId, userRole);

    // Return appropriate file URL based on access level
    if (!fileAccess.fileUrl) {
      return next(
        new ErrorResponse(`Access denied. You need to purchase this content to access it.`, 403)
      );
    }

    res.status(200).json({
      success: true,
      data: {
        contentId: content._id,
        accessType: fileAccess.accessType,
        fileUrl: fileAccess.fileUrl,
        reason: fileAccess.reason,
        canAccessFull: fileAccess.canAccessFull,
        hasPreview: fileAccess.hasPreview
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get content preview (public access)
// @route   GET /api/content/:id/preview
// @access  Public
exports.getContentPreview = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.id);

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${req.params.id}`, 404)
      );
    }

    // Check if content is published
    if (content.status !== "Published") {
      return next(
        new ErrorResponse(`Content is not available`, 404)
      );
    }

    // Check if preview exists
    if (!content.previewUrl) {
      return next(
        new ErrorResponse(`Preview not available for this content`, 404)
      );
    }

    res.status(200).json({
      success: true,
      data: {
        contentId: content._id,
        previewUrl: content.previewUrl,
        accessType: 'preview',
        message: 'This is a preview version. Purchase the content to access the full version.'
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Test preview generation (development only)
// @route   POST /api/content/test-preview
// @access  Private/Seller/Admin
exports.testPreviewGeneration = async (req, res, next) => {
  try {
    const { fileUrl, contentType, fileName } = req.body;

    if (!fileUrl || !contentType || !fileName) {
      return res.status(400).json({
        success: false,
        message: 'fileUrl, contentType, and fileName are required'
      });
    }

    console.log(`[Test] Testing preview generation for: ${fileName}`);

    // Check if preview can be generated
    if (!canGeneratePreview(contentType, fileName)) {
      return res.status(400).json({
        success: false,
        message: `Preview generation not supported for ${contentType} files with extension: ${path.extname(fileName)}`
      });
    }

    // Determine if file is on S3 or local
    const isS3Upload = isS3Url(fileUrl);

    // Generate preview
    const previewUrl = await generatePreview(
      contentType,
      isS3Upload ? fileUrl : path.join('./uploads/', fileName),
      fileName,
      isS3Upload
    );

    if (previewUrl) {
      res.status(200).json({
        success: true,
        data: {
          originalFile: fileUrl,
          previewUrl: previewUrl,
          contentType: contentType,
          fileName: fileName,
          isS3Upload: isS3Upload
        },
        message: 'Preview generated successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Preview generation failed - returned null'
      });
    }
  } catch (err) {
    console.error('[Test] Preview generation test failed:', err);
    next(err);
  }
};
